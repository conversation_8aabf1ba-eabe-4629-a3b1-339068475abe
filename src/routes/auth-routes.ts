/**
 * Authentication Routes (Simplified)
 *
 * Better Auth integration routes for Hono.
 * Handles basic authentication endpoints without middleware dependencies.
 * This is a temporary simplified version to avoid global scope async operations.
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { getAuth } from '../auth';

const authApp = new Hono();

// CORS configuration for auth routes
authApp.use(
  '*',
  cors({
    origin: ['http://localhost:3000', 'https://localhost:3000', 'http://localhost:8787'],
    allowHeaders: ['Content-Type', 'Authorization', 'Cookie'],
    allowMethods: ['POST', 'GET', 'OPTIONS'],
    exposeHeaders: ['Set-Cookie'],
    maxAge: 600,
    credentials: true,
  })
);

/**
 * Better Auth public endpoints (no authentication required)
 */

// Sign up endpoint
authApp.post('/sign-up/email', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-up error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Registration failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Sign in endpoint
authApp.post('/sign-in/email', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-in error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Login failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Sign out endpoint
authApp.post('/sign-out', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-out error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Logout failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Get session endpoint (public, but returns different data based on auth status)
authApp.get('/session', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Session error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Session check failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Health check for auth service
authApp.get('/health', async (c) => {
  try {
    const _auth = await getAuth();

    return c.json({
      status: 'success',
      service: 'Better Auth',
      version: '1.0.0',
      healthy: true,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Auth health check error:', error);

    return c.json(
      {
        status: 'error',
        service: 'Better Auth',
        healthy: false,
        error: 'Service unavailable',
        timestamp: new Date().toISOString(),
      },
      503
    );
  }
});

// Catch-all for other Better Auth endpoints
authApp.on(['POST', 'GET', 'DELETE'], '/*', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Auth handler error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Authentication service error',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

export { authApp };
  '*',
  cors({
    origin: ['http://localhost:3000', 'https://localhost:3000', 'http://localhost:8787'],
    allowHeaders: ['Content-Type', 'Authorization', 'Cookie'],
    allowMethods: ['POST', 'GET', 'OPTIONS'],
    exposeHeaders: ['Set-Cookie'],
    maxAge: 600,
    credentials: true,
  })
);

/**
 * Better Auth public endpoints (no authentication required)
 */

// Sign up endpoint
authApp.post('/sign-up/email', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-up error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Registration failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Sign in endpoint
authApp.post('/sign-in/email', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-in error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Login failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Sign out endpoint
authApp.post('/sign-out', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Sign-out error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Logout failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Get session endpoint (public, but returns different data based on auth status)
authApp.get('/session', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Session error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Session check failed',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// Catch-all for other Better Auth endpoints
authApp.on(['POST', 'GET', 'DELETE'], '/*', async (c) => {
  try {
    const auth = await getAuth();
    return await auth.handler(c.req.raw);
  } catch (error) {
    console.error('Auth handler error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Authentication service error',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * Get current session endpoint
 *
 * Returns the current user session information.
 * This is a convenience endpoint that uses the middleware session.
 * TEMPORARILY DISABLED - requires authMiddleware
 */
// authApp.get('/user-session', authMiddleware, (c) => {
//   const user = c.get('user');
//   const session = c.get('session');
//   const isAuthenticated = c.get('isAuthenticated');
//
//   if (!isAuthenticated || !user || !session) {
//     return c.json(
//       {
//         status: 'error',
//         error: 'No active session',
//         error_type: 'authentication',
//         timestamp: new Date().toISOString(),
//       },
//       401
//     );
//   }
//
//   return c.json({
//     status: 'success',
//     data: {
//       user: {
//         id: user.id,
//         name: user.name,
//         email: user.email,
//         emailVerified: user.emailVerified,
//         image: user.image,
//         role: user.role,
//         createdAt: user.createdAt,
//         updatedAt: user.updatedAt,
//       },
//       session: {
//         id: session.id,
//         expiresAt: session.expiresAt,
//         createdAt: session.createdAt,
//         updatedAt: session.updatedAt,
//       },
//     },
//     timestamp: new Date().toISOString(),
//   });
// });

/**
 * Get user profile endpoint
 *
 * Returns the current user's profile information.
 * TEMPORARILY DISABLED - requires authMiddleware
 */
// authApp.get('/me', authMiddleware, (c) => {
//   const user = c.get('user');
//   const isAuthenticated = c.get('isAuthenticated');
//
//   if (!isAuthenticated || !user) {
//     return c.json(
//       {
//         status: 'error',
//         error: 'Authentication required',
//         error_type: 'authentication',
//         timestamp: new Date().toISOString(),
//       },
//       401
//     );
//   }
//
//   return c.json({
//     status: 'success',
//     data: {
//       id: user.id,
//       name: user.name,
//       email: user.email,
//       emailVerified: user.emailVerified,
//       image: user.image,
//       role: user.role,
//       createdAt: user.createdAt,
//       updatedAt: user.updatedAt,
//     },
//     timestamp: new Date().toISOString(),
//   });
// });

/**
 * Health check for auth service
 */
authApp.get('/health', async (c) => {
  try {
    const _auth = await getAuth();

    return c.json({
      status: 'success',
      service: 'Better Auth',
      version: '1.0.0',
      healthy: true,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Auth health check error:', error);

    return c.json(
      {
        status: 'error',
        service: 'Better Auth',
        healthy: false,
        error: 'Service unavailable',
        timestamp: new Date().toISOString(),
      },
      503
    );
  }
});

/**
 * List active sessions endpoint
 *
 * Returns all active sessions for the current user.
 * TEMPORARILY DISABLED - requires authMiddleware
 */
// authApp.get('/user-sessions', authMiddleware, async (c) => {
  const user = c.get('user');
  const isAuthenticated = c.get('isAuthenticated');

  if (!isAuthenticated || !user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  try {
    const _auth = await getAuth();

    // Note: This would require implementing a custom endpoint in Better Auth
    // For now, return the current session
    const session = c.get('session');

    return c.json({
      status: 'success',
      data: {
        sessions: session ? [session] : [],
        total: session ? 1 : 0,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('List sessions error:', error);

    return c.json(
      {
        status: 'error',
        error: 'Failed to retrieve sessions',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * Revoke session endpoint
 *
 * Revokes a specific session by token.
 */
authApp.delete('/user-sessions/:token', authMiddleware, async (c) => {
  const user = c.get('user');
  const isAuthenticated = c.get('isAuthenticated');

  if (!isAuthenticated || !user) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  const token = c.req.param('token');

  if (!token) {
    return c.json(
      {
        status: 'error',
        error: 'Session token is required',
        error_type: 'validation',
        timestamp: new Date().toISOString(),
      },
      400
    );
  }

  try {
    const _auth = await getAuth();

    // Note: This would require implementing session revocation in Better Auth
    // For now, return success

    return c.json({
      status: 'success',
      message: 'Session revoked successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Revoke session error:', error);

    return c.json(
      {
        status: 'error',
        error: 'Failed to revoke session',
        error_type: 'internal',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

export { authApp };
